/**
 * Centralized utilities for resolution-based configurations (Node.js)
 * Avoids scattered resolution mapping logic across the media server
 */

/**
 * Get video dimensions for a given resolution
 */
function getResolutionDimensions(resolution) {
  switch (resolution) {
    case '720p':
      return { width: 1280, height: 720 };
    case '1080p':
      return { width: 1920, height: 1080 };
    default:
      return { width: 1920, height: 1080 }; // Default to 1080p
  }
}

/**
 * Get bitrate configurations for a given resolution
 */
function getResolutionBitrates(resolution) {
  switch (resolution) {
    case '720p':
      return {
        maxBitrate: 2500000, // 2.5Mbps in bps
        startBitrate: 2500,  // 2.5Mbps in kbps
        sdpBitrate: '2500'   // for SDP AS: field
      };
    case '1080p':
      return {
        maxBitrate: 5000000, // 5Mbps in bps
        startBitrate: 5000,  // 5Mbps in kbps
        sdpBitrate: '5000'   // for SDP AS: field
      };
    default:
      return {
        maxBitrate: 5000000,
        startBitrate: 5000,
        sdpBitrate: '5000'
      };
  }
}

/**
 * Validate if a string is a valid resolution
 */
function isValidResolution(value) {
  return value === '720p' || value === '1080p';
}

/**
 * Get default resolution (1080p)
 */
function getDefaultResolution() {
  return '1080p';
}

module.exports = {
  getResolutionDimensions,
  getResolutionBitrates,
  isValidResolution,
  getDefaultResolution
};
