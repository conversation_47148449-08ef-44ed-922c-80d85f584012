import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './AuthContext';
import { getResolutionDimensions } from '../utils/resolutionUtils';

interface Peer {
  id: string;
  connection: RTCPeerConnection;
  stream?: MediaStream;
}

interface ConnectionContextType {
  peersRef: React.RefObject<Record<string, Peer>>;
  peerIds: string[];
  localStream: MediaStream | null;
  joinProduction: (productionId: string, isHost: boolean) => Promise<void>;
  leaveProduction: () => void;
  toggleAudio: () => void;
  toggleVideo: () => void;
  toggleScreenShare: (resolution?: '720p' | '1080p') => Promise<void>;
  isAudioEnabled: boolean;
  isVideoEnabled: boolean;
  isScreenSharing: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected';
  connectionType: 'unknown' | 'local' | 'internet' | 'relay';
}

const ConnectionContext = createContext<ConnectionContextType | null>(null);

export const useConnection = () => {
  const context = useContext(ConnectionContext);
  if (!context) {
    throw new Error('useConnection must be used within a ConnectionProvider');
  }
  return context;
};

export const ConnectionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { token } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [peerIds, setPeerIds] = useState<string[]>([]);
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [connectionType, setConnectionType] = useState<'unknown' | 'local' | 'internet' | 'relay'>('unknown');

  // Use refs to avoid stale closure issues and prevent infinite loops
  const peersRef = useRef<Record<string, Peer>>({});
  const socketRef = useRef<Socket | null>(null);
  const localStreamRef = useRef<MediaStream | null>(null);
  const connectionStatusRef = useRef<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const isJoiningRef = useRef(false);
  const currentRoomRef = useRef<string | null>(null);



  // Helper function to analyze connection type from WebRTC stats
  const analyzeConnectionType = useCallback(async (peerConnection: RTCPeerConnection) => {
    try {
      // Add a small delay to ensure stats are available
      await new Promise(resolve => setTimeout(resolve, 1000));

      const stats = await peerConnection.getStats();
      let connectionType: 'unknown' | 'local' | 'internet' | 'relay' = 'unknown';
      let foundSuccessfulPair = false;

      // First, collect all candidates by ID
      const candidates = new Map();
      stats.forEach(report => {
        if (report.type === 'local-candidate' || report.type === 'remote-candidate') {
          candidates.set(report.id, report);
        }
      });

      console.log(`📋 Found ${candidates.size} ICE candidates`);

      // Then analyze candidate pairs
      stats.forEach(report => {
        if (report.type === 'candidate-pair' && report.state === 'succeeded') {
          foundSuccessfulPair = true;

          // Get candidate details using IDs
          const localCandidate = candidates.get(report.localCandidateId);
          const remoteCandidate = candidates.get(report.remoteCandidateId);

          const localType = localCandidate?.candidateType;
          const remoteType = remoteCandidate?.candidateType;

          console.log(`📊 Active candidate pair details:`, {
            localId: report.localCandidateId,
            remoteId: report.remoteCandidateId,
            localType,
            remoteType,
            localCandidate: localCandidate ? {
              candidateType: localCandidate.candidateType,
              address: localCandidate.address,
              port: localCandidate.port,
              protocol: localCandidate.protocol
            } : 'not found',
            remoteCandidate: remoteCandidate ? {
              candidateType: remoteCandidate.candidateType,
              address: remoteCandidate.address,
              port: remoteCandidate.port,
              protocol: remoteCandidate.protocol
            } : 'not found'
          });

          // Determine connection type based on candidate types
          // Priority: relay > internet > local (most specific first)
          if (localType === 'relay' || remoteType === 'relay') {
            connectionType = 'relay'; // Using TURN server
          } else if ((localType === 'srflx' || remoteType === 'srflx') && connectionType !== 'relay') {
            connectionType = 'internet'; // Internet connection via STUN
          } else if (localType === 'host' && remoteType === 'host' && connectionType === 'unknown') {
            connectionType = 'local'; // Direct local network connection
          }
        }
      });

      if (!foundSuccessfulPair) {
        console.warn('⚠️ No successful candidate pairs found in stats');
        // Log all available reports for debugging
        console.log('📋 Available stats reports:');
        stats.forEach(report => {
          console.log(`  - ${report.type}:`, report);
        });
      }

      setConnectionType(connectionType);
      console.log(`🔗 Connection type detected: ${connectionType}`);
    } catch (error) {
      console.error('❌ Failed to analyze connection type:', error);
    }
  }, []);

  // Helper function to add a peer and trigger re-render
  const addPeer = useCallback((peer: Peer) => {
    peersRef.current[peer.id] = peer;
    setPeerIds(Object.keys(peersRef.current)); // Trigger re-render
  }, []);

  // Helper function to remove a peer and trigger re-render
  const removePeer = useCallback((peerId: string) => {
    if (peersRef.current[peerId]) {
      delete peersRef.current[peerId];
      setPeerIds(Object.keys(peersRef.current)); // Trigger re-render
    }
  }, []);

  // Helper function to update a peer and trigger re-render
  const updatePeer = useCallback((peerId: string, updates: Partial<Peer>) => {
    if (peersRef.current[peerId]) {
      peersRef.current[peerId] = { ...peersRef.current[peerId], ...updates };
      setPeerIds(Object.keys(peersRef.current)); // Trigger re-render
    }
  }, []);

  useEffect(() => {
    socketRef.current = socket;
  }, [socket]);

  useEffect(() => {
    connectionStatusRef.current = connectionStatus;
  }, [connectionStatus]);

  useEffect(() => {
    localStreamRef.current = localStream;

    // Add tracks to existing peer connections when local stream becomes available
    if (localStream) {
      const currentPeers = peersRef.current;
      Object.entries(currentPeers).forEach(([peerId, peer]) => {
        const existingSenders = peer.connection.getSenders();
        const localTracks = localStream.getTracks();

        // Check if we need to add tracks
        localTracks.forEach(track => {
          const existingSender = existingSenders.find(sender =>
            sender.track && sender.track.kind === track.kind
          );

          if (!existingSender) {
            peer.connection.addTrack(track, localStream);
          }
        });
      });
    }
  }, [localStream]);

  const rtcConfig: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      // Keep your TURN server as backup for difficult networks
      {
        urls: `turn:${import.meta.env.VITE_TURN_SERVER || 'YOUR_TURN_SERVER_IP'}:3478`,
        username: import.meta.env.VITE_TURN_USERNAME || 'webrtc',
        credential: import.meta.env.VITE_TURN_PASSWORD || 'webrtc123'
      }
    ],
    iceCandidatePoolSize: 10,
   // iceTransportPolicy: 'relay' as RTCIceTransportPolicy
  };

  const createPeerConnection = useCallback((peerId: string, isInitiator: boolean) => {
    // Check if peer connection already exists
    const existingPeer = peersRef.current[peerId];
    if (existingPeer) {
      return existingPeer.connection;
    }

    const pc = new RTCPeerConnection(rtcConfig);

    // Fix: Set up ICE candidate handler IMMEDIATELY after creation
    pc.onicecandidate = (event) => {
      if (event.candidate && socketRef.current) {
        socketRef.current.emit('ice-candidate', {
          candidate: event.candidate,
          to: peerId,
        });
      }
    };

    pc.ontrack = (event) => {
      const [remoteStream] = event.streams;
      console.log(`📺 Received remote stream from ${peerId}`);

      const currentPeer = peersRef.current[peerId];
      if (!currentPeer) {
        addPeer({
          id: peerId,
          connection: pc,
          stream: remoteStream,
        });
      } else {
        updatePeer(peerId, { stream: remoteStream });
      }
    };

    pc.onconnectionstatechange = () => {
      console.log(`🔗 Peer ${peerId} connection state: ${pc.connectionState}`);

      // Update global connection status based on peer connections
      if (pc.connectionState === 'connected') {
        console.log(`✅ Connected to peer ${peerId}`);
        setConnectionStatus('connected');
        // Analyze connection type when connection is established
        analyzeConnectionType(pc);
      } else if (pc.connectionState === 'failed') {
        console.error(`❌ Connection failed to peer ${peerId}`);
        const currentPeers = peersRef.current;
        const hasConnectedPeers = Object.values(currentPeers).some(peer =>
          peer.connection.connectionState === 'connected'
        );
        if (!hasConnectedPeers) {
          setConnectionStatus('disconnected');
          setConnectionType('unknown');
        }
      } else if (pc.connectionState === 'disconnected') {
        console.warn(`⚠️ Disconnected from peer ${peerId}`);
        const currentPeers = peersRef.current;
        const hasConnectedPeers = Object.values(currentPeers).some(peer =>
          peer.connection.connectionState === 'connected'
        );
        if (!hasConnectedPeers) {
          setConnectionStatus('disconnected');
          setConnectionType('unknown');
        }
      } else if (pc.connectionState === 'connecting') {
        if (connectionStatusRef.current !== 'connected') {
          setConnectionStatus('connecting');
        }
      }
    };

    pc.oniceconnectionstatechange = () => {
      if (pc.iceConnectionState === 'failed') {
        console.warn(`⚠️ ICE connection failed for ${peerId}, restarting ICE`);
        pc.restartIce();
      }
    };

    // Add local stream tracks - use ref to get current stream
    const currentLocalStream = localStreamRef.current;
    if (currentLocalStream) {
      const tracks = currentLocalStream.getTracks();

      tracks.forEach(track => {
        pc.addTrack(track, currentLocalStream);
      });
    }

    const newPeer = {
      id: peerId,
      connection: pc,
    };

    addPeer(newPeer);

    return pc;
  }, [analyzeConnectionType]);

  const joinProduction = useCallback(async (productionId: string, isHost: boolean) => {
    // Prevent multiple simultaneous join attempts
    if (isJoiningRef.current) {
      return;
    }

    // Check if already in the same production
    if (currentRoomRef.current === productionId && socketRef.current?.connected) {
      return;
    }

    try {
      isJoiningRef.current = true;
      setConnectionStatus('connecting');

      // Clean up any existing connection first
      if (socketRef.current) {
        socketRef.current.disconnect();
        setSocket(null);
      }

      // Get user media only if we don't have it already
      let stream = localStreamRef.current;
      if (!stream) {
        try {
          stream = await navigator.mediaDevices.getUserMedia({
            video: { width: 1280, height: 720 },
            audio: { echoCancellation: true, noiseSuppression: true },
          });
          console.log('✅ Media access granted');
        } catch (error) {
          console.error('❌ Failed to get user media:', error);
          throw error; // Re-throw to handle upstream
        }

        // Update both state and ref immediately to ensure availability for peer connections
        setLocalStream(stream);
        localStreamRef.current = stream;
      }

      // Connect to signaling server
      const socketUrl = window.location.hostname === 'localhost'
        ? 'http://localhost:3001'
        : undefined;

      const newSocket = io(socketUrl, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true,
        auth: {
          token: token
        }
      });
      setSocket(newSocket);
      currentRoomRef.current = productionId;

      // Add connection error handling
      newSocket.on('connect_error', (error) => {
        console.error('❌ Socket connection error:', error);
        setConnectionStatus('disconnected');
        isJoiningRef.current = false;
      });

      newSocket.on('connect', () => {
        console.log('🔌 Connected to signaling server');
        newSocket.emit('join-production', { productionId, isHost });
        isJoiningRef.current = false; // Mark joining as complete
      });

      newSocket.on('user-joined', ({ peerId }) => {
        console.log('👤 User joined:', peerId);
        // Don't create peer connection here - wait for initiate-connection signal
      });

      newSocket.on('initiate-connection', ({ peerId }) => {
        // Create the peer connection as initiator
        const pc = createPeerConnection(peerId, true);

        pc.createOffer().then(offer => {
          return pc.setLocalDescription(offer);
        }).then(() => {
          newSocket.emit('offer', {
            offer: pc.localDescription,
            to: peerId,
          });
        }).catch(error => {
          console.error(`❌ Failed to create offer for ${peerId}:`, error);
        });
      });

      newSocket.on('offer', async ({ offer, from }) => {
        // Create peer connection when receiving offer (as non-initiator)
        const pc = createPeerConnection(from, false);

        try {
          await pc.setRemoteDescription(offer);

          const answer = await pc.createAnswer();
          await pc.setLocalDescription(answer);

          newSocket.emit('answer', {
            answer: pc.localDescription,
            to: from,
          });
        } catch (error) {
          console.error(`❌ Failed to handle offer from ${from}:`, error);
        }
      });

      newSocket.on('answer', async ({ answer, from }) => {
        // Use ref to get current peers to avoid stale closure
        const peer = peersRef.current[from];
        if (peer) {
          try {
            // Check if we're in the right state to set remote description
            if (peer.connection.signalingState === 'stable') {
              return; // Skip setting the answer if already stable
            }

            await peer.connection.setRemoteDescription(answer);
          } catch (error) {
            console.error(`❌ Failed to set remote description from ${from}:`, error);
          }
        }
      });

      newSocket.on('ice-candidate', async ({ candidate, from }) => {
        // Use ref to get current peers to avoid stale closure
        const peer = peersRef.current[from];
        if (peer) {
          try {
            await peer.connection.addIceCandidate(candidate);
          } catch (error) {
            console.error(`❌ Failed to add ICE candidate from ${from}:`, error);
          }
        }
      });

      newSocket.on('user-left', ({ peerId }) => {
        console.log('👤 User left:', peerId);
        const peer = peersRef.current[peerId];
        if (peer) {
          peer.connection.close();
          removePeer(peerId);
        }
      });

      newSocket.on('error', ({ message }) => {
        console.error('❌ Server error:', message);
      });

      newSocket.on('disconnect', (reason) => {
        console.warn('⚠️ Disconnected from signaling server:', reason);
        currentRoomRef.current = null;
      });

    } catch (error) {
      console.error('❌ Failed to join production:', error);
      setConnectionStatus('disconnected');
      isJoiningRef.current = false;
      currentRoomRef.current = null;
    }
  }, [createPeerConnection]);

  const finishProduction = useCallback(() => {
    console.log('🏁 Finishing production and cleaning up media server...');

    // Reset joining flag
    isJoiningRef.current = false;

    // Emit finish-production event to media server before disconnecting
    if (socketRef.current) {
      socketRef.current.emit('finish-production');
      // Give a small delay to ensure the event is sent before disconnecting
      setTimeout(() => {
        if (socketRef.current) {
          socketRef.current.disconnect();
          setSocket(null);
        }
      }, 100);
    }

    const currentPeers = peersRef.current;
    Object.values(currentPeers).forEach(peer => {
      peer.connection.close();
    });

    // Clear all peers
    peersRef.current = {};
    setPeerIds([]);

    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach(track => track.stop());
      setLocalStream(null);
    }

    currentRoomRef.current = null;
    setConnectionStatus('disconnected');
  }, []);

  const leaveProduction = useCallback(() => {
    // Reset joining flag
    isJoiningRef.current = false;

    if (socketRef.current) {
      socketRef.current.disconnect();
      setSocket(null);
    }

    const currentPeers = peersRef.current;
    Object.values(currentPeers).forEach(peer => {
      peer.connection.close();
    });

    // Clear all peers
    peersRef.current = {};
    setPeerIds([]);

    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach(track => track.stop());
      setLocalStream(null);
    }

    currentRoomRef.current = null;
    setConnectionStatus('disconnected');
  }, []);

  const toggleAudio = useCallback(() => {
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsAudioEnabled(audioTrack.enabled);
        console.log(`🎤 Audio ${audioTrack.enabled ? 'enabled' : 'disabled'}`);
      }
    }
  }, [localStream]);

  const toggleVideo = useCallback(() => {
    if (localStream) {
      const videoTrack = localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsVideoEnabled(videoTrack.enabled);
        console.log(`📹 Video ${videoTrack.enabled ? 'enabled' : 'disabled'}`);
      }
    }
  }, [localStream]);

  const toggleScreenShare = useCallback(async (resolution: '720p' | '1080p' = '1080p') => {
    try {
      if (isScreenSharing) {
        // Stop screen sharing, switch back to camera
        // Get camera stream with resolution-appropriate dimensions
        const cameraDimensions = getResolutionDimensions(resolution);
        const cameraStream = await navigator.mediaDevices.getUserMedia({
          video: { width: cameraDimensions.width, height: cameraDimensions.height },
          audio: { echoCancellation: true, noiseSuppression: true },
        });

        // Replace tracks in peer connections
        const currentPeers = peersRef.current;
        Object.values(currentPeers).forEach(peer => {
          const senders = peer.connection.getSenders();
          const videoSender = senders.find(sender => sender.track?.kind === 'video');
          const audioSender = senders.find(sender => sender.track?.kind === 'audio');

          if (videoSender && cameraStream.getVideoTracks()[0]) {
            videoSender.replaceTrack(cameraStream.getVideoTracks()[0]);
          }
          if (audioSender && cameraStream.getAudioTracks()[0]) {
            audioSender.replaceTrack(cameraStream.getAudioTracks()[0]);
          }
        });

        // Stop old stream
        if (localStreamRef.current) {
          localStreamRef.current.getTracks().forEach(track => track.stop());
        }

        // Update stream
        setLocalStream(cameraStream);
        localStreamRef.current = cameraStream;
        setIsScreenSharing(false);
        console.log('📹 Switched back to camera');

      } else {
        // Start screen sharing with resolution-appropriate dimensions
        console.log('🖥️ Starting screen share...');
        const screenDimensions = getResolutionDimensions(resolution);
        const screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: { width: screenDimensions.width, height: screenDimensions.height },
          audio: true
        });

        // Add audio from microphone if screen doesn't have audio
        if (screenStream.getAudioTracks().length === 0 && localStreamRef.current) {
          const audioTracks = localStreamRef.current.getAudioTracks();
          if (audioTracks.length > 0) {
            screenStream.addTrack(audioTracks[0]);
          }
        }

        // Replace tracks in peer connections
        const currentPeers = peersRef.current;
        Object.values(currentPeers).forEach(peer => {
          const senders = peer.connection.getSenders();
          const videoSender = senders.find(sender => sender.track?.kind === 'video');
          const audioSender = senders.find(sender => sender.track?.kind === 'audio');

          if (videoSender && screenStream.getVideoTracks()[0]) {
            videoSender.replaceTrack(screenStream.getVideoTracks()[0]);
          }
          if (audioSender && screenStream.getAudioTracks()[0]) {
            audioSender.replaceTrack(screenStream.getAudioTracks()[0]);
          }
        });

        // Handle screen share ending (user clicks "Stop sharing" in browser)
        screenStream.getVideoTracks()[0].addEventListener('ended', () => {
          toggleScreenShare(resolution); // This will switch back to camera
        });

        // Stop old stream
        if (localStreamRef.current) {
          localStreamRef.current.getTracks().forEach(track => track.stop());
        }

        // Update stream
        setLocalStream(screenStream);
        localStreamRef.current = screenStream;
        setIsScreenSharing(true);
        console.log('🖥️ Screen sharing started');
      }
    } catch (error) {
      console.error('❌ Failed to toggle screen share:', error);
    }
  }, [isScreenSharing]);

  // Cleanup on unmount only - don't include leaveRoom in dependencies to avoid infinite loop
  useEffect(() => {
    return () => {
      // Use refs for cleanup to avoid stale closures
      if (socketRef.current) {
        socketRef.current.disconnect();
      }

      Object.values(peersRef.current).forEach(peer => {
        peer.connection.close();
      });

      if (localStreamRef.current) {
        localStreamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []); // Empty dependency array - only run on mount/unmount

  return (
    <ConnectionContext.Provider
      value={{
        peersRef,
        peerIds,
        localStream,
        joinProduction,
        leaveProduction,
        toggleAudio,
        toggleVideo,
        toggleScreenShare,
        isAudioEnabled,
        isVideoEnabled,
        isScreenSharing,
        connectionStatus,
        connectionType,
      }}
    >
      {children}
    </ConnectionContext.Provider>
  );
};
