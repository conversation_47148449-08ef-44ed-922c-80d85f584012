/**
 * Centralized utilities for resolution-based configurations
 * Avoids scattered resolution mapping logic across the codebase
 */

export type Resolution = '720p' | '1080p';

export interface ResolutionDimensions {
  width: number;
  height: number;
}

export interface ResolutionBitrates {
  maxBitrate: number; // in bps (e.g., 5000000 for 5Mbps)
  startBitrate: number; // in kbps (e.g., 5000 for 5Mbps)
  sdpBitrate: string; // for SDP files (e.g., "5000" for 5Mbps)
}

/**
 * Get video dimensions for a given resolution
 */
export function getResolutionDimensions(resolution: Resolution): ResolutionDimensions {
  switch (resolution) {
    case '720p':
      return { width: 1280, height: 720 };
    case '1080p':
      return { width: 1920, height: 1080 };
    default:
      return { width: 1920, height: 1080 }; // Default to 1080p
  }
}

/**
 * Get bitrate configurations for a given resolution
 */
export function getResolutionBitrates(resolution: Resolution): ResolutionBitrates {
  switch (resolution) {
    case '720p':
      return {
        maxBitrate: 2500000, // 2.5Mbps in bps
        startBitrate: 2500,  // 2.5Mbps in kbps
        sdpBitrate: '2500'   // for SDP AS: field
      };
    case '1080p':
      return {
        maxBitrate: 5000000, // 5Mbps in bps
        startBitrate: 5000,  // 5Mbps in kbps
        sdpBitrate: '5000'   // for SDP AS: field
      };
    default:
      return {
        maxBitrate: 5000000,
        startBitrate: 5000,
        sdpBitrate: '5000'
      };
  }
}

/**
 * Get human-readable quality label for UI display
 */
export function getResolutionLabel(resolution: Resolution): string {
  switch (resolution) {
    case '720p':
      return '720p - Standard Quality';
    case '1080p':
      return '1080p - High Quality';
    default:
      return '1080p - High Quality';
  }
}

/**
 * Validate if a string is a valid resolution
 */
export function isValidResolution(value: string): value is Resolution {
  return value === '720p' || value === '1080p';
}

/**
 * Get default resolution (1080p)
 */
export function getDefaultResolution(): Resolution {
  return '1080p';
}
