import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  UnauthorizedException,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { TokenRefreshService } from './token-refresh.service';
import { UsersService } from '../users/users.service';

/**
 * Interceptor that automatically refreshes OAuth tokens when API calls fail due to expired tokens
 * This interceptor catches 401 errors and attempts to refresh the token before retrying the request
 */
@Injectable()
export class TokenRefreshInterceptor implements NestInterceptor {
  constructor(
    private tokenRefreshService: TokenRefreshService,
    private usersService: UsersService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError((error) => {
        // Check if this is a 401 error that might be due to expired token
        if (this.isTokenExpiredError(error)) {
          return this.handleTokenRefresh(context, next, error);
        }
        
        // If it's not a token-related error, just re-throw it
        return throwError(() => error);
      }),
    );
  }

  private isTokenExpiredError(error: any): boolean {
    // Check for common token expiration error patterns
    if (error instanceof UnauthorizedException) {
      const message = error.message?.toLowerCase() || '';
      return (
        message.includes('token') && 
        (message.includes('expired') || message.includes('invalid'))
      );
    }

    // Check for Google API specific errors
    if (error?.response?.status === 401) {
      const errorMessage = error.response?.data?.error?.message?.toLowerCase() || '';
      return (
        errorMessage.includes('invalid_token') ||
        errorMessage.includes('token_expired') ||
        errorMessage.includes('credentials')
      );
    }

    // Check for Facebook API specific errors
    if (error?.response?.status === 401 || error?.response?.status === 400) {
      const errorCode = error.response?.data?.error?.code;
      const errorType = error.response?.data?.error?.type;
      
      // Facebook error codes for expired/invalid tokens
      return (
        errorCode === 190 || // Invalid OAuth access token
        errorCode === 102 || // Session key invalid
        errorType === 'OAuthException'
      );
    }

    return false;
  }

  private async handleTokenRefresh(
    context: ExecutionContext,
    next: CallHandler,
    originalError: any,
  ): Promise<Observable<any>> {
    try {
      const request = context.switchToHttp().getRequest();
      const userId = this.extractUserIdFromRequest(request);
      
      if (!userId) {
        return throwError(() => originalError);
      }

      const user = await this.usersService.findById(userId);
      if (!user) {
        return throwError(() => originalError);
      }

      // Try to refresh tokens for all linked platforms
      const refreshPromises = user.linkedPlatforms
        .filter(platform => platform.provider !== 'local')
        .map(async (platform) => {
          try {
            if (platform.provider === 'google') {
              await this.tokenRefreshService.refreshGoogleToken(user);
            } else if (platform.provider === 'facebook') {
              await this.tokenRefreshService.refreshFacebookToken(user);
            }
          } catch (refreshError) {
            // Log but don't fail the entire operation if one platform fails
            console.warn(`Failed to refresh ${platform.provider} token:`, refreshError.message);
          }
        });

      await Promise.allSettled(refreshPromises);

      // Retry the original request
      return next.handle();
    } catch (refreshError) {
      // If token refresh fails, return the original error
      return throwError(() => originalError);
    }
  }

  private extractUserIdFromRequest(request: any): string | null {
    // Try to extract user ID from JWT token in request
    if (request.user?.userId) {
      return request.user.userId;
    }

    // Try to extract from user object
    if (request.user?.sub) {
      return request.user.sub;
    }

    // Try to extract from user ID parameter
    if (request.params?.userId) {
      return request.params.userId;
    }

    return null;
  }
}
