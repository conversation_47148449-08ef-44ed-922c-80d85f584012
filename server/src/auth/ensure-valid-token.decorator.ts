import { SetMetadata } from '@nestjs/common';

export const ENSURE_VALID_TOKEN_KEY = 'ensureValidToken';

/**
 * Decorator to mark methods that should automatically refresh OAuth tokens before execution
 * @param platforms - Array of platforms to ensure valid tokens for ('google', 'facebook')
 */
export const EnsureValidToken = (...platforms: ('google' | 'facebook')[]) =>
  SetMetadata(ENSURE_VALID_TOKEN_KEY, platforms);
