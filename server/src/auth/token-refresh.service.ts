import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { google } from 'googleapis';
import axios from 'axios';
import { UserDocument } from '../users/schemas/user.schema';
import { UsersService } from '../users/users.service';

@Injectable()
export class TokenRefreshService {
  private readonly logger = new Logger(TokenRefreshService.name);

  constructor(
    private configService: ConfigService,
    private usersService: UsersService,
  ) {}

  /**
   * Refresh Google OAuth token using refresh token
   */
  async refreshGoogleToken(user: UserDocument): Promise<void> {
    const googlePlatform = user.linkedPlatforms.find(p => p.provider === 'google');
    
    if (!googlePlatform) {
      throw new UnauthorizedException('Google platform not linked');
    }

    if (!googlePlatform.refreshToken) {
      throw new UnauthorizedException('No refresh token available for Google. Please re-authenticate.');
    }

    const oauth2Client = new google.auth.OAuth2(
      this.configService.get<string>('GOOGLE_CLIENT_ID'),
      this.configService.get<string>('GOOGLE_CLIENT_SECRET')
    );
    
    oauth2Client.setCredentials({
      refresh_token: googlePlatform.refreshToken
    });

    try {
      this.logger.log(`Refreshing Google token for user ${user._id}`);
      
      const { credentials } = await oauth2Client.refreshAccessToken();
      
      // Update the stored tokens
      googlePlatform.accessToken = credentials.access_token;
      
      // Google may provide a new refresh token
      if (credentials.refresh_token) {
        googlePlatform.refreshToken = credentials.refresh_token;
      }
      
      // Set expiration time (Google tokens typically expire in 1 hour)
      googlePlatform.tokenExpiresAt = credentials.expiry_date 
        ? new Date(credentials.expiry_date)
        : new Date(Date.now() + 3600 * 1000); // Fallback to 1 hour
      
      await user.save();
      
      this.logger.log(`Successfully refreshed Google token for user ${user._id}`);
    } catch (error) {
      this.logger.error(`Failed to refresh Google token for user ${user._id}:`, error.message);
      
      // If refresh fails, the refresh token might be invalid
      // Clear the tokens to force re-authentication
      googlePlatform.accessToken = undefined;
      googlePlatform.refreshToken = undefined;
      googlePlatform.tokenExpiresAt = undefined;
      await user.save();
      
      throw new UnauthorizedException('Failed to refresh Google token. Please re-authenticate.');
    }
  }

  /**
   * Refresh Facebook OAuth token using refresh token or long-lived token exchange
   */
  async refreshFacebookToken(user: UserDocument): Promise<void> {
    const facebookPlatform = user.linkedPlatforms.find(p => p.provider === 'facebook');
    
    if (!facebookPlatform) {
      throw new UnauthorizedException('Facebook platform not linked');
    }

    if (!facebookPlatform.accessToken) {
      throw new UnauthorizedException('No access token available for Facebook. Please re-authenticate.');
    }

    const appId = this.configService.get<string>('FACEBOOK_APP_ID');
    const appSecret = this.configService.get<string>('FACEBOOK_APP_SECRET');

    if (!appId || !appSecret) {
      throw new UnauthorizedException('Facebook app credentials not configured');
    }

    try {
      this.logger.log(`Refreshing Facebook token for user ${user._id}`);
      
      // Facebook token refresh/extension endpoint
      const response = await axios.get('https://graph.facebook.com/oauth/access_token', {
        params: {
          grant_type: 'fb_exchange_token',
          client_id: appId,
          client_secret: appSecret,
          fb_exchange_token: facebookPlatform.accessToken,
        },
      });

      const { access_token, expires_in } = response.data;
      
      // Update the stored tokens
      facebookPlatform.accessToken = access_token;
      
      // Set expiration time based on expires_in (usually 60 days for long-lived tokens)
      facebookPlatform.tokenExpiresAt = expires_in 
        ? new Date(Date.now() + expires_in * 1000)
        : new Date(Date.now() + 60 * 24 * 3600 * 1000); // Fallback to 60 days
      
      await user.save();
      
      this.logger.log(`Successfully refreshed Facebook token for user ${user._id}`);
    } catch (error) {
      this.logger.error(`Failed to refresh Facebook token for user ${user._id}:`, error.message);
      
      // If refresh fails, clear the tokens to force re-authentication
      facebookPlatform.accessToken = undefined;
      facebookPlatform.refreshToken = undefined;
      facebookPlatform.tokenExpiresAt = undefined;
      await user.save();
      
      throw new UnauthorizedException('Failed to refresh Facebook token. Please re-authenticate.');
    }
  }

  /**
   * Check if a token is expired or will expire soon (within 5 minutes)
   */
  isTokenExpiredOrExpiring(tokenExpiresAt: Date | undefined, bufferMinutes: number = 5): boolean {
    if (!tokenExpiresAt) {
      return true; // Consider undefined expiration as expired
    }
    
    const bufferTime = bufferMinutes * 60 * 1000; // Convert to milliseconds
    const expirationWithBuffer = new Date(tokenExpiresAt.getTime() - bufferTime);
    
    return new Date() >= expirationWithBuffer;
  }

  /**
   * Automatically refresh token if needed for a specific platform
   */
  async ensureValidToken(user: UserDocument, platform: 'google' | 'facebook'): Promise<void> {
    const platformData = user.linkedPlatforms.find(p => p.provider === platform);
    
    if (!platformData) {
      throw new UnauthorizedException(`${platform} platform not linked`);
    }

    if (this.isTokenExpiredOrExpiring(platformData.tokenExpiresAt)) {
      this.logger.log(`Token for ${platform} is expired or expiring soon, refreshing...`);
      
      if (platform === 'google') {
        await this.refreshGoogleToken(user);
      } else if (platform === 'facebook') {
        await this.refreshFacebookToken(user);
      }
    }
  }

  /**
   * Get a valid access token for a platform, refreshing if necessary
   */
  async getValidAccessToken(user: UserDocument, platform: 'google' | 'facebook'): Promise<string> {
    await this.ensureValidToken(user, platform);
    
    // Reload user to get updated tokens
    const updatedUser = await this.usersService.findById(user._id.toString());
    const platformData = updatedUser.linkedPlatforms.find(p => p.provider === platform);
    
    if (!platformData?.accessToken) {
      throw new UnauthorizedException(`No valid access token available for ${platform}`);
    }
    
    return platformData.accessToken;
  }
}
