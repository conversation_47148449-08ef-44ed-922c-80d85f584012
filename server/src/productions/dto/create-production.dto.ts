import { IsString, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>th, IsDateString, IsEnum, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateStreamingDestinationDto {
  @IsString()
  @IsEnum(['youtube', 'facebook'])
  platform: 'youtube' | 'facebook';

  @IsString()
  channelId: string;

  @IsString()
  @IsOptional()
  channelName?: string;

  @IsOptional()
  @IsEnum(['public', 'unlisted', 'private'])
  privacy?: 'public' | 'unlisted' | 'private' = 'unlisted';
}

export class CreateProductionDto {
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @IsOptional()
  @IsString()
  @MinLength(6)
  @MaxLength(6)
  productionId?: string; // If not provided, will be auto-generated

  @IsOptional()
  @IsDateString()
  scheduledStartTime?: string; // If provided, production starts as 'scheduled', otherwise 'live'

  @IsOptional()
  @IsEnum(['public', 'unlisted', 'private'])
  youtubePrivacy?: 'public' | 'unlisted' | 'private' = 'unlisted';

  @IsOptional()
  @IsEnum(['720p', '1080p'])
  resolution?: '720p' | '1080p' = '1080p';

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateStreamingDestinationDto)
  destinations?: CreateStreamingDestinationDto[];
}
