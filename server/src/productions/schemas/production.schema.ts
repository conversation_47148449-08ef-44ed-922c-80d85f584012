import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ProductionDocument = Production & Document;

// Schema for streaming destinations
@Schema({ _id: false })
export class StreamingDestination {
  @Prop({ required: true, type: String, enum: ['youtube', 'facebook'] })
  platform: string;

  @Prop({ required: true })
  channelId: string; // YouTube channel ID or Facebook page ID

  @Prop()
  channelName?: string; // Display name for the channel/page

  @Prop()
  broadcastId?: string; // Platform-specific broadcast ID

  @Prop()
  streamId?: string; // Platform-specific stream ID

  @Prop()
  rtmpUrl?: string; // RTMP endpoint for this destination

  @Prop()
  streamKey?: string; // Stream key for this destination

  @Prop({ default: 'unlisted' })
  privacy?: 'public' | 'unlisted' | 'private';

  @Prop({ default: Date.now })
  createdAt: Date;
}

export const StreamingDestinationSchema = SchemaFactory.createForClass(StreamingDestination);

@Schema({ timestamps: true })
export class Production {
  @Prop({ required: true, unique: true })
  productionId: string; // The 6-character production ID

  @Prop({ required: true })
  name: string;

  @Prop()
  description?: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  hostUserId: Types.ObjectId;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'User' }], default: [] })
  participantUserIds: Types.ObjectId[];

  // Scheduling fields (from Event schema)
  @Prop()
  scheduledStartTime?: Date;

  @Prop()
  wentLiveAt?: Date;

  @Prop()
  completedAt?: Date;

  // Streaming fields
  @Prop({ default: false })
  isStreaming: boolean;

  @Prop()
  rtmpUrl?: string;

  @Prop()
  rtmpStreamKey?: string;

  @Prop()
  streamStartedAt?: Date;

  @Prop()
  streamEndedAt?: Date;

  // Unified status: scheduled -> live -> completed/cancelled
  @Prop({ default: 'scheduled' })
  status: 'scheduled' | 'live' | 'completed' | 'cancelled';

  @Prop({ default: Date.now })
  lastActivityAt: Date;

  // Streaming destinations (replaces individual YouTube fields)
  @Prop({ type: [StreamingDestinationSchema], default: [] })
  destinations: StreamingDestination[];

  // Legacy YouTube integration fields (kept for backward compatibility)
  @Prop()
  youtubeBroadcastId?: string;

  @Prop()
  youtubeStreamId?: string;

  @Prop({ default: 'unlisted' })
  youtubePrivacy?: 'public' | 'unlisted' | 'private';

  // Stream quality setting for the entire production
  @Prop({ default: '1080p' })
  resolution?: '720p' | '1080p';
}

export const ProductionSchema = SchemaFactory.createForClass(Production);

// Create indexes for efficient lookups
// Note: productionId index is created automatically by unique: true
ProductionSchema.index({ hostUserId: 1 });
ProductionSchema.index({ participantUserIds: 1 });
ProductionSchema.index({ status: 1, lastActivityAt: 1 });
ProductionSchema.index({ hostUserId: 1, status: 1 }); // For finding user's active production
ProductionSchema.index({ status: 1 });
ProductionSchema.index({ scheduledStartTime: 1 });
ProductionSchema.index({ hostUserId: 1, scheduledStartTime: 1 });
