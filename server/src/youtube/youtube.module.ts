import { Module } from '@nestjs/common';
import { YouTubeService } from './youtube.service';
import { YouTube<PERSON>ontroller } from './youtube.controller';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [UsersModule, AuthModule],
  controllers: [YouTubeController],
  providers: [YouTubeService],
  exports: [YouTubeService],
})
export class YouTubeModule {}
